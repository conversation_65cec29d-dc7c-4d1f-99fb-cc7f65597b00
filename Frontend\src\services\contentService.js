import api from "./api";
import { CONTENT_ENDPOINTS } from "../utils/constants";

/**
 * Get all content
 * @param {Object} params - Query parameters for filtering, pagination, etc.
 * @returns {Promise} Promise with content data
 */
export const getAllContent = async (params = {}) => {
  const response = await api.get(CONTENT_ENDPOINTS.ALL, { params });
  // Transform the response to match the expected structure
  return {
    ...response.data,
    content: response.data.data // Map data to content
  };
};

/**
 * Get content categories
 * @returns {Promise} Promise with categories data
 */
export const getContentCategories = async () => {
  const response = await api.get(CONTENT_ENDPOINTS.CATEGORIES);
  return response.data;
};

/**
 * Get trending content
 * @returns {Promise} Promise with trending content data
 */
export const getTrendingContent = async () => {
  const response = await api.get(CONTENT_ENDPOINTS.TRENDING);
  return response.data;
};

/**
 * Get single content by ID
 * @param {string} id - Content ID
 * @returns {Promise} Promise with content data
 */
export const getContent = async (id) => {
  const response = await api.get(CONTENT_ENDPOINTS.SINGLE(id));
  return response.data;
};

/**
 * Create content (seller only)
 * @param {Object} contentData - Content data
 * @returns {Promise} Promise with created content data
 */
export const createContent = async (contentData) => {
  const response = await api.post(CONTENT_ENDPOINTS.ALL, contentData);
  return response.data;
};

/**
 * Update content (seller only)
 * @param {string} id - Content ID
 * @param {Object} contentData - Content data to update
 * @returns {Promise} Promise with updated content data
 */
export const updateContent = async (id, contentData) => {
  const response = await api.put(CONTENT_ENDPOINTS.SINGLE(id), contentData);
  return response.data;
};

/**
 * Delete content (seller only)
 * @param {string} id - Content ID
 * @returns {Promise} Promise with success message
 */
export const deleteContent = async (id) => {
  const response = await api.delete(CONTENT_ENDPOINTS.SINGLE(id));
  return response.data;
};

/**
 * Get seller content (seller only)
 * @param {Object} params - Query parameters for filtering, pagination, etc.
 * @returns {Promise} Promise with seller content data
 */
export const getSellerContent = async (params = {}) => {
  const response = await api.get(CONTENT_ENDPOINTS.SELLER_CONTENT, { params });
  return response.data;
};

/**
 * Get single seller content by ID (seller only)
 * @param {string} id - Content ID
 * @returns {Promise} Promise with content data
 */
export const getSellerContentById = async (id) => {
  const response = await api.get(`${CONTENT_ENDPOINTS.BASE}/seller/${id}`);
  return response.data;
};

/**
 * Upload content file (seller only) with enhanced error handling and retry logic
 * @param {FormData} formData - Form data with file
 * @param {Function} onProgress - Progress callback function
 * @param {number} retryCount - Current retry attempt (default: 0)
 * @returns {Promise} Promise with upload result
 */
export const uploadContentFile = async (formData, onProgress, retryCount = 0) => {
  const maxRetries = 2; // Allow up to 2 retries
  const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  console.log(`[Upload-${uploadId}] Starting upload attempt ${retryCount + 1}/${maxRetries + 1}`);

  // Get file info for logging
  const file = formData.get('file');
  if (file) {
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    console.log(`[Upload-${uploadId}] File: ${file.name} (${fileSizeMB}MB, ${file.type})`);
  }

  try {
    const response = await api.post(CONTENT_ENDPOINTS.UPLOAD, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      timeout: 3600000, // 1 hour timeout specifically for file uploads
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          const loadedMB = Math.round(progressEvent.loaded / (1024 * 1024));
          const totalMB = Math.round(progressEvent.total / (1024 * 1024));

          console.log(`[Upload-${uploadId}] Progress: ${percentCompleted}% (${loadedMB}MB/${totalMB}MB)`);
          onProgress(percentCompleted);
        }
      }
    });

    console.log(`[Upload-${uploadId}] Upload successful on attempt ${retryCount + 1}`);
    return response.data;

  } catch (error) {
    console.error(`[Upload-${uploadId}] Upload failed on attempt ${retryCount + 1}:`, {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    // Check if error is retryable
    const isRetryable = (
      error.code === 'NETWORK_ERROR' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNABORTED' ||
      error.message?.includes('Network Error') ||
      error.message?.includes('timeout') ||
      (error.response?.status >= 500 && error.response?.status < 600)
    );

    // Retry if possible and error is retryable
    if (retryCount < maxRetries && isRetryable) {
      const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
      console.log(`[Upload-${uploadId}] Retrying in ${retryDelay}ms...`);

      await new Promise(resolve => setTimeout(resolve, retryDelay));
      return uploadContentFile(formData, onProgress, retryCount + 1);
    }

    // Format error for user
    let userMessage = 'Network error. Please check your connection.';
    if (error.response?.data?.message) {
      userMessage = error.response.data.message;
    } else if (error.message?.includes('timeout')) {
      userMessage = 'Upload timed out. Please try again with a smaller file or better connection.';
    } else if (error.code === 'ECONNRESET') {
      userMessage = 'Connection was reset. Please check your internet connection and try again.';
    }

    const enhancedError = new Error(userMessage);
    enhancedError.originalError = error;
    enhancedError.status = error.response?.status || 500;
    enhancedError.code = error.code;
    enhancedError.retryCount = retryCount;

    throw enhancedError;
  }
};

/**
 * Toggle content active status (seller only)
 * @param {string} id - Content ID
 * @returns {Promise} Promise with updated content data
 */
export const toggleContentStatus = async (id) => {
  const response = await api.put(
    `${CONTENT_ENDPOINTS.BASE}/${id}/toggle-status`
  );
  return response.data;
};

/**
 * Get preview generation status
 * @param {string} id - Content ID
 * @returns {Promise} Promise with preview status data
 */
export const getPreviewStatus = async (id) => {
  const response = await api.get(
    `${CONTENT_ENDPOINTS.BASE}/${id}/preview-status`
  );
  return response.data;
};

export default {
  getAllContent,
  getContentCategories,
  getTrendingContent,
  getContent,
  createContent,
  updateContent,
  deleteContent,
  getSellerContent,
  getSellerContentById,
  uploadContentFile,
  toggleContentStatus,
};
