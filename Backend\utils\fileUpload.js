const multer = require('multer');
const path = require('path');
const fs = require('fs');
const ErrorResponse = require('./errorResponse');
const { getS3Instance, hasAWSCredentials } = require('./storageHelper');
const networkMonitor = require('./networkMonitor');

// Get S3 instance from storage helper
const s3 = getS3Instance();

// Ensure directory exists
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dirPath}`);
  }
};

// File type and size limits
const FILE_LIMITS = {
  Video: {
    maxSize: 1024 * 1024 * 1024, // 1GB - Updated for large video files
    allowedTypes: /mp4|mov|avi|webm/,
    allowedMimes: [
      'video/mp4',
      'video/quicktime',
      'video/x-msvideo',
      'video/webm'
    ]
  },
  Document: {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: /pdf/,
    allowedMimes: [
      'application/pdf'
    ]
  },
  Image: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: /jpeg|jpg|png|gif/,
    allowedMimes: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ]
  }
};

// Check file type with specific validation for different file types
const checkFileType = (file, cb) => {
  // Determine file category based on field name or content type
  let category = 'Document'; // Default to Document

  if (file.fieldname === 'thumbnail' || file.fieldname === 'profileImage' || file.mimetype.startsWith('image/')) {
    category = 'Image';
  } else if (file.mimetype.startsWith('video/')) {
    category = 'Video';
  }

  const limits = FILE_LIMITS[category];

  // Check file size
  if (file.size > limits.maxSize) {
    const maxSizeMB = limits.maxSize / (1024 * 1024);
    return cb(new ErrorResponse(`File size must be less than ${maxSizeMB}MB`, 400));
  }

  // Check file extension
  const extname = limits.allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = limits.allowedMimes.includes(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    const allowedExts = limits.allowedTypes.toString().replace(/[/|]/g, ', ').toUpperCase();
    return cb(new ErrorResponse(`Only ${allowedExts} formats are allowed`, 400));
  }
};

// Custom storage engine that handles both S3 and local storage
const customStorage = {
  _handleFile: function (req, file, cb) {
    const timestamp = Date.now();
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '-');
    const fileName = `${timestamp}-${sanitizedName}`;

    // Force profile images to always use local storage
    if (file.fieldname === 'profileImage') {
      console.log('[FileUpload] Starting local upload for profile image:', file.originalname);
      const folder = './uploads/profiles/';

      // Ensure directory exists
      if (!fs.existsSync(folder)) {
        fs.mkdirSync(folder, { recursive: true });
        console.log('[FileUpload] Created directory:', folder);
      }

      const filePath = path.join(folder, fileName);
      const writeStream = fs.createWriteStream(filePath);

      let uploadedBytes = 0;

      // Track upload progress
      file.stream.on('data', (chunk) => {
        uploadedBytes += chunk.length;
        // Log progress every 10MB for large files
        if (uploadedBytes % (10 * 1024 * 1024) === 0) {
          console.log(`[FileUpload] Local upload progress: ${Math.round(uploadedBytes / (1024 * 1024))}MB uploaded`);
        }
      });

      file.stream.pipe(writeStream);

      writeStream.on('error', (err) => {
        console.error('[FileUpload] Local upload error:', err);
        cb(err);
      });

      writeStream.on('finish', () => {
        console.log('[FileUpload] Profile image upload successful:', filePath);
        console.log(`[FileUpload] Final size: ${Math.round(uploadedBytes / (1024 * 1024))}MB`);
        cb(null, {
          filename: fileName,
          path: filePath,
          size: uploadedBytes
        });
      });
    } else if (hasAWSCredentials() && s3) {
      // S3 upload for non-profile files
      console.log('[FileUpload] Starting S3 upload for:', file.originalname);
      const s3Key = `uploads/${file.fieldname}/${fileName}`;

      const uploadParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: s3Key,
        Body: file.stream,
        ContentType: file.mimetype,
        // Removed ACL setting - bucket policy handles public access
        Metadata: {
          fieldName: file.fieldname,
          originalName: file.originalname,
          uploadTime: timestamp.toString()
        }
      };

      // Use managed upload with optimized settings for large files
      const managedUpload = s3.upload(uploadParams, {
        partSize: 10 * 1024 * 1024, // 10MB parts for multipart uploads
        queueSize: 1, // Process one part at a time to avoid memory issues
        leavePartsOnError: false // Clean up failed multipart uploads
      });

      // Enhanced progress tracking with detailed logging
      let lastLoggedPercent = 0;
      let uploadStartTime = Date.now();
      let lastProgressTime = Date.now();

      managedUpload.on('httpUploadProgress', (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        const loadedMB = Math.round(progress.loaded / (1024 * 1024));
        const totalMB = Math.round(progress.total / (1024 * 1024));
        const currentTime = Date.now();
        const elapsedSeconds = Math.round((currentTime - uploadStartTime) / 1000);
        const timeSinceLastProgress = currentTime - lastProgressTime;

        // Update network monitor
        networkMonitor.updateProgress(uploadId, percent, progress.loaded);

        // Log every 5% or if more than 30 seconds since last progress
        if (percent >= lastLoggedPercent + 5 || timeSinceLastProgress > 30000) {
          const speed = loadedMB / (elapsedSeconds || 1);
          const remainingMB = totalMB - loadedMB;
          const estimatedTimeRemaining = remainingMB / (speed || 1);

          console.log(`[FileUpload] S3 upload progress: ${percent}% (${loadedMB}MB/${totalMB}MB)`);
          console.log(`[FileUpload] Upload speed: ${speed.toFixed(2)} MB/s, Elapsed: ${elapsedSeconds}s, ETA: ${Math.round(estimatedTimeRemaining)}s`);
          console.log(`[FileUpload] Part info: ~${Math.ceil(progress.loaded / (10 * 1024 * 1024))} parts uploaded`);

          lastLoggedPercent = percent;
        }

        lastProgressTime = currentTime;
      });

      // Add error event listener for more detailed error reporting
      managedUpload.on('error', (error) => {
        console.error(`[FileUpload] S3 upload error event:`, {
          message: error.message,
          code: error.code,
          statusCode: error.statusCode,
          retryable: error.retryable,
          time: error.time,
          stack: error.stack
        });
      });

      // Add part upload events for detailed tracking
      managedUpload.on('httpError', (error, response) => {
        console.error(`[FileUpload] S3 HTTP error:`, {
          statusCode: response?.statusCode,
          statusMessage: response?.statusMessage,
          error: error.message,
          headers: response?.headers
        });
      });

      // Log when multipart upload starts
      console.log(`[FileUpload] Starting S3 multipart upload for ${file.originalname}`);
      console.log(`[FileUpload] Upload configuration:`, {
        bucket: process.env.AWS_BUCKET_NAME,
        key: s3Key,
        contentType: file.mimetype,
        partSize: '10MB',
        queueSize: 1
      });

      // Start network monitoring
      const uploadId = `s3_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
      networkMonitor.startUpload(uploadId, {
        fileName: file.originalname,
        fileSize: file.size || 0,
        contentType: file.mimetype,
        s3Key: s3Key,
        bucket: process.env.AWS_BUCKET_NAME
      });

      managedUpload.send((err, data) => {
        const uploadEndTime = Date.now();
        const totalUploadTime = Math.round((uploadEndTime - uploadStartTime) / 1000);

        if (err) {
          // Complete network monitoring with failure
          networkMonitor.completeUpload(uploadId, false, err);

          console.error(`[FileUpload] S3 upload failed after ${totalUploadTime} seconds`);
          console.error('[FileUpload] Comprehensive error details:', {
            message: err.message,
            code: err.code,
            statusCode: err.statusCode,
            retryable: err.retryable,
            requestId: err.requestId,
            extendedRequestId: err.extendedRequestId,
            cfId: err.cfId,
            region: err.region,
            hostname: err.hostname,
            retryDelay: err.retryDelay,
            time: err.time,
            originalError: err.originalError,
            stack: err.stack?.substring(0, 500) // Limit stack trace length
          });

          // Provide more specific error messages based on error type
          let userFriendlyMessage = 'File upload failed';
          if (err.code === 'NetworkingError' || err.code === 'UnknownEndpoint') {
            userFriendlyMessage = 'Network connection lost during upload. Please check your internet connection and try again.';
          } else if (err.code === 'TimeoutError' || err.code === 'RequestTimeout') {
            userFriendlyMessage = 'Upload timed out. Please try uploading a smaller file or check your connection.';
          } else if (err.statusCode === 403) {
            userFriendlyMessage = 'Access denied. Please contact support.';
          } else if (err.statusCode === 413) {
            userFriendlyMessage = 'File too large. Maximum file size is 1GB.';
          } else if (err.code === 'RequestAbortedError') {
            userFriendlyMessage = 'Upload was interrupted. Please try again.';
          }

          const enhancedError = new Error(userFriendlyMessage);
          enhancedError.originalError = err;
          enhancedError.uploadDuration = totalUploadTime;
          enhancedError.code = err.code;
          enhancedError.statusCode = err.statusCode;
          return cb(enhancedError);
        }

        // Complete network monitoring with success
        networkMonitor.completeUpload(uploadId, true);

        const fileSizeMB = Math.round((file.size || 0) / (1024 * 1024));
        const avgSpeed = fileSizeMB / (totalUploadTime || 1);

        console.log(`[FileUpload] S3 upload successful: ${data.Location}`);
        console.log(`[FileUpload] Upload completed in ${totalUploadTime}s at ${avgSpeed.toFixed(2)} MB/s`);
        console.log(`[FileUpload] Final file details:`, {
          size: fileSizeMB + 'MB',
          bucket: data.Bucket,
          key: data.Key,
          etag: data.ETag,
          location: data.Location
        });

        cb(null, {
          bucket: data.Bucket,
          key: data.Key,
          location: data.Location,
          etag: data.ETag,
          size: file.size,
          // Store additional info for signed URL generation
          s3Key: data.Key,
          s3Bucket: data.Bucket,
          uploadDuration: totalUploadTime,
          avgSpeed: avgSpeed
        });
      });
    } else {
      // Local upload for non-profile files
      console.log('[FileUpload] Starting local upload for:', file.originalname);
      const folder = './uploads/';

      // Ensure directory exists
      if (!fs.existsSync(folder)) {
        fs.mkdirSync(folder, { recursive: true });
        console.log('[FileUpload] Created directory:', folder);
      }

      const filePath = path.join(folder, fileName);
      const writeStream = fs.createWriteStream(filePath);

      let uploadedBytes = 0;

      // Track upload progress
      file.stream.on('data', (chunk) => {
        uploadedBytes += chunk.length;
        // Log progress every 10MB for large files
        if (uploadedBytes % (10 * 1024 * 1024) === 0) {
          console.log(`[FileUpload] Local upload progress: ${Math.round(uploadedBytes / (1024 * 1024))}MB uploaded`);
        }
      });

      file.stream.pipe(writeStream);

      writeStream.on('error', (err) => {
        console.error('[FileUpload] Local upload error:', err);
        cb(err);
      });

      writeStream.on('finish', () => {
        console.log('[FileUpload] Local upload successful:', filePath);
        console.log(`[FileUpload] Final size: ${Math.round(uploadedBytes / (1024 * 1024))}MB`);
        cb(null, {
          filename: fileName,
          path: filePath,
          size: uploadedBytes
        });
      });
    }
  },

  _removeFile: function (req, file, cb) {
    // Cleanup function if needed
    cb(null);
  }
};

// Use custom storage that handles both S3 and local automatically
const storage = customStorage;
console.log('[FileUpload] Using custom storage engine (auto-detects S3/Local)');

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 1073741824, // 1GB for video files (1024 * 1024 * 1024)
    fieldSize: 50000000,  // 50MB for field data
    files: 10             // Maximum 10 files
  },
  fileFilter: function (req, file, cb) {
    checkFileType(file, cb);
  }
});

// Add error handling wrapper for upload
const uploadWithErrorHandling = {
  single: (fieldName) => {
    return (req, res, next) => {
      const uploadSingle = upload.single(fieldName);
      uploadSingle(req, res, (err) => {
        if (err) {
          console.error('[FileUpload] Upload error:', err);

          // If S3 error and we have S3 configured, try to provide more details
          if (err.message && err.message.includes('client.send')) {
            console.error('[FileUpload] S3 client error detected. This might be due to AWS SDK configuration issues.');
            return next(new ErrorResponse('File upload failed due to storage configuration issue. Please try again or contact support.', 500));
          }

          return next(new ErrorResponse(`File upload failed: ${err.message}`, 400));
        }
        next();
      });
    };
  }
};

// Export both upload middleware and helper functions
module.exports = {
  upload: uploadWithErrorHandling,
  uploadRaw: upload, // Export raw upload for diagnostics
  hasAWSCredentials,
  isUsingS3Storage: () => hasAWSCredentials() && s3 !== null
};
