# Large File Upload Debug Improvements

## Overview
This document outlines the comprehensive debugging improvements made to address the 980MB video upload issue that was getting stuck at 33% with "Network error" and "ERR_CONNECTION_RESET".

## Problem Analysis
The upload was failing at 33% of 980MB (≈323MB), which corresponds to around the 32nd part in S3's 10MB multipart upload system. This suggested:
- Network timeout or connection reset during multipart upload
- Insufficient logging to diagnose the exact failure point
- Lack of retry mechanisms for transient network issues

## Improvements Made

### 1. Enhanced Backend S3 Upload Logging (`Backend/utils/fileUpload.js`)

#### Progress Tracking Enhancements:
- **Detailed progress logging**: Now logs every 5% progress or every 30 seconds
- **Speed calculation**: Shows current upload speed in MB/s
- **ETA estimation**: Calculates estimated time remaining
- **Part tracking**: Shows how many 10MB parts have been uploaded
- **Timing analysis**: Tracks elapsed time and detects stalled uploads

#### Error Handling Improvements:
- **Comprehensive error logging**: Captures all AWS SDK error details including:
  - Error codes (NetworkingError, TimeoutError, etc.)
  - Status codes and HTTP details
  - Request IDs for AWS support
  - Retry information
- **User-friendly error messages**: Translates technical errors into actionable messages
- **Error categorization**: Identifies network vs. server vs. client errors

#### Network Monitoring Integration:
- **Upload lifecycle tracking**: Monitors from start to completion
- **Stuck upload detection**: Identifies uploads with no progress for >30 seconds
- **System resource logging**: Tracks memory, CPU, and load during uploads

### 2. Enhanced Route Handler Logging (`Backend/routes/content.js`)

#### Request Tracking:
- **Unique request IDs**: Each upload gets a unique identifier for log correlation
- **Request metadata logging**: Captures headers, content-length, user info
- **Timeout monitoring**: Logs when timeouts occur
- **Processing time tracking**: Measures time from request to response

#### Enhanced Error Responses:
- **Structured error responses**: Consistent error format with status codes
- **Debug information**: Includes upload duration and error context
- **User-friendly messages**: Clear error messages for different failure types

### 3. Frontend Retry Logic (`Frontend/src/services/contentService.js`)

#### Automatic Retry System:
- **Exponential backoff**: 1s, 2s, 4s delays between retries
- **Smart retry logic**: Only retries network-related errors
- **Retry tracking**: Logs retry attempts and final outcomes
- **Maximum retry limit**: Prevents infinite retry loops

#### Enhanced Progress Tracking:
- **Detailed progress logging**: Logs progress with MB transferred
- **Upload identification**: Unique IDs for frontend upload tracking
- **Error context**: Preserves original error details through retries

### 4. Frontend Error Handling (`Frontend/src/pages/Seller/AddStrategy.jsx` & `EditStrategy.jsx`)

#### Improved User Feedback:
- **Specific error messages**: Different messages for different error types
- **File size display**: Shows uploaded file size in success messages
- **Retry indication**: Informs users when retries are happening
- **Connection guidance**: Provides specific advice for network issues

#### Enhanced Logging:
- **Upload lifecycle tracking**: Logs start, progress, and completion
- **Error categorization**: Identifies and logs different error types
- **Debug information**: Captures retry counts and error details

### 5. Network Monitoring System (`Backend/utils/networkMonitor.js`)

#### Real-time Upload Monitoring:
- **Active upload tracking**: Monitors all ongoing uploads
- **Progress analysis**: Detects stuck or slow uploads
- **System resource monitoring**: Tracks server performance during uploads
- **Metrics collection**: Gathers success rates, average speeds, error counts

#### Diagnostic Capabilities:
- **Stuck upload detection**: Identifies uploads with no progress >60 seconds
- **Performance metrics**: Tracks upload speeds and completion times
- **Error analysis**: Categorizes and counts different error types
- **Resource monitoring**: Logs memory, CPU, and system load

### 6. Diagnostic Endpoint (`Backend/routes/diagnostics.js`)

#### Network Status API:
- **Real-time monitoring**: `/api/diagnostics/network-status` endpoint
- **Active upload status**: Shows current uploads and their progress
- **Stuck upload alerts**: Identifies problematic uploads
- **Performance metrics**: Overall upload statistics and success rates

### 7. Test Utilities (`Backend/scripts/test-large-upload.js`)

#### Upload Testing Tools:
- **Synthetic file generation**: Creates test files of any size
- **Progress monitoring**: Detailed upload progress tracking
- **Error simulation**: Tests various failure scenarios
- **Performance measurement**: Measures upload speeds and completion times

## Usage Instructions

### For Debugging Current Issues:

1. **Check server logs** for detailed upload progress and error information
2. **Monitor network status** via `/api/diagnostics/network-status` endpoint
3. **Review frontend console** for client-side error details and retry attempts

### For Testing Large Uploads:

1. **Set environment variables**:
   ```bash
   export TEST_AUTH_TOKEN="your-seller-jwt-token"
   export SERVER_URL="http://localhost:5000"
   ```

2. **Run test script**:
   ```bash
   cd Backend
   node scripts/test-large-upload.js
   ```

### Log Analysis:

Look for these log patterns to diagnose issues:

- `[Upload-{requestId}]` - Request-level tracking
- `[FileUpload]` - S3 upload process
- `[NetworkMonitor]` - Upload monitoring and metrics
- `[Upload-{uploadId}]` - Frontend upload tracking

## Expected Improvements

1. **Better Error Visibility**: Detailed logs will show exactly where uploads fail
2. **Automatic Recovery**: Retry logic handles transient network issues
3. **Performance Monitoring**: Real-time tracking of upload health
4. **User Experience**: Clear error messages and progress feedback
5. **Debugging Capability**: Comprehensive logging for issue diagnosis

## Monitoring Recommendations

1. **Watch for stuck uploads**: Check diagnostic endpoint regularly
2. **Monitor error patterns**: Look for recurring error codes or messages
3. **Track performance metrics**: Monitor upload speeds and success rates
4. **System resource usage**: Ensure server has adequate resources during large uploads

## Next Steps

1. **Test with the 980MB file** that was previously failing
2. **Monitor logs** during upload to see detailed progress
3. **Check network status** if upload appears stuck
4. **Review error messages** for specific guidance on failures
5. **Use retry logic** to handle transient network issues automatically
