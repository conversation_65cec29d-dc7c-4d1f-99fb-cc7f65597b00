const express = require("express");
const { check } = require("express-validator");
const {
  getAllContent,
  getContent,
  createContent,
  updateContent,
  deleteContent,
  getSellerContent,
  getSellerContentById,
  getContentCategories,
  getTrendingContent,
  toggleContentStatus,
  getContentAccess,
  getContentPreview,
  getPreviewStatus,
  testPreviewGeneration,
} = require("../controllers/content");

const { protect, authorize } = require("../middleware/auth");
const { upload } = require("../utils/fileUpload");
const { getFileUrl, getSignedUrl, isS3Url } = require("../utils/storageHelper");
const { convertContentS3Urls } = require("../middleware/s3UrlHandler");

const router = express.Router();

// Public routes with S3 URL conversion
router.get("/", convertContentS3Urls, getAllContent);
router.get("/categories", getContentCategories);
router.get("/trending", convertContentS3Urls, getTrendingContent);
router.get("/:id/preview", convertContentS3Urls, getContentPreview);
router.get("/:id/preview-status", getPreviewStatus);
router.get("/:id", convertContentS3Urls, getContent);

// Handle CORS preflight for streaming endpoint
router.options("/stream/:fileKey(*)", (req, res) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
    "https://localhost:3000",
    "https://localhost:5173",
    "https://xosportshub.thefabaf.com",
    "https://xosports.thefabaf.com",
    "http://xosportshub.thefabaf.com",
    "http://xosports.thefabaf.com",
    process.env.FRONTEND_URL,
  ].filter(Boolean);

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
  res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length');
  res.status(200).end();
});

// Stream video files from S3 with proper headers for video playback
router.get(
  "/stream/:fileKey(*)",
  async (req, res, next) => {
    try {
      const fileKey = req.params.fileKey;
      const range = req.headers.range;
      const token = req.query.token || req.headers.authorization?.replace('Bearer ', '');

      // Verify token manually since we're bypassing the protect middleware
      console.log(`[Video Stream] Token from query: ${req.query.token ? 'present' : 'missing'}`);
      console.log(`[Video Stream] Token from header: ${req.headers.authorization ? 'present' : 'missing'}`);
      console.log(`[Video Stream] Final token: ${token ? 'present' : 'missing'}`);

      if (!token) {
        console.log(`[Video Stream] No token provided`);
        return res.status(401).json({
          success: false,
          message: "Access token is required"
        });
      }

      // Verify the JWT token
      const jwt = require('jsonwebtoken');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        console.log(`[Video Stream] Token verified successfully for user: ${decoded.id}`);
      } catch (error) {
        console.log(`[Video Stream] Token verification failed:`, error.message);
        return res.status(401).json({
          success: false,
          message: "Invalid token"
        });
      }

      if (!fileKey) {
        return res.status(400).json({
          success: false,
          message: "File key is required"
        });
      }

      console.log(`[Video Stream] Streaming video for: ${fileKey}`);

      const { getS3Instance, isUsingS3Storage } = require("../utils/storageHelper");

      if (!isUsingS3Storage()) {
        return res.status(400).json({
          success: false,
          message: "S3 storage not configured"
        });
      }

      const s3 = getS3Instance();
      if (!s3) {
        return res.status(500).json({
          success: false,
          message: "S3 instance not available"
        });
      }

      // Get object metadata first
      const headParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: fileKey
      };

      const headResult = await s3.headObject(headParams).promise();
      const fileSize = headResult.ContentLength;
      const contentType = headResult.ContentType || 'video/mp4';

      // Set CORS headers
      const origin = req.headers.origin;
      const allowedOrigins = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "https://localhost:3000",
        "https://localhost:5173",
        "https://xosportshub.thefabaf.com",
        "https://xosports.thefabaf.com",
        "http://xosportshub.thefabaf.com",
        "http://xosports.thefabaf.com",
        process.env.FRONTEND_URL,
      ].filter(Boolean);

      if (allowedOrigins.includes(origin)) {
        res.setHeader('Access-Control-Allow-Origin', origin);
      }
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
      res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length');

      // Handle range requests for video seeking
      if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;

        const getParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: fileKey,
          Range: `bytes=${start}-${end}`
        };

        res.status(206);
        res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Length', chunksize);
        res.setHeader('Content-Type', contentType);

        const stream = s3.getObject(getParams).createReadStream();
        stream.pipe(res);
      } else {
        // Full file request
        const getParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: fileKey
        };

        res.setHeader('Content-Length', fileSize);
        res.setHeader('Content-Type', contentType);
        res.setHeader('Accept-Ranges', 'bytes');

        const stream = s3.getObject(getParams).createReadStream();
        stream.pipe(res);
      }

    } catch (error) {
      console.error('[Video Stream] Error streaming video:', error);
      if (error.code === 'NoSuchKey') {
        return res.status(404).json({
          success: false,
          message: "Video file not found"
        });
      }
      next(error);
    }
  }
);

// Protected routes
router.use(protect);

// Secure content access routes
router.get("/:id/access", convertContentS3Urls, getContentAccess);

// Seller routes
router.get("/seller/me", authorize("seller"), convertContentS3Urls, getSellerContent);
router.get("/seller/:id", authorize("seller"), convertContentS3Urls, getSellerContentById);

router.post(
  "/",
  authorize("seller"),
  [
    // Required fields that are visible in frontend
    check("title", "Title is required").not().isEmpty()
      .isLength({ max: 100 }).withMessage("Title cannot exceed 100 characters"),
    check("description", "Description is required").not().isEmpty(),
    check("sport", "Sport is required").not().isEmpty(),
    check("contentType", "Content type is required").not().isEmpty(),
    check("fileUrl", "File URL is required").not().isEmpty(),
    check("category", "Category is required").not().isEmpty(),
    check("difficulty", "Difficulty level is required").not().isEmpty(),
    check("aboutCoach", "About coach information is required").not().isEmpty(),
    check("strategicContent", "Strategic content description is required")
      .not()
      .isEmpty(),
    check("saleType", "Sale type is required").not().isEmpty(),

    // Optional fields that are used in frontend with default values
    check("language", "Language is required")
      .optional()
      .isIn([
        "English",
        "Spanish",
        "French",
        "German",
        "Italian",
        "Portuguese",
        "Chinese",
        "Japanese",
        "Korean",
        "Other",
      ]),

    // COMMENTED OUT - Fields not used in current frontend UI
    // check('videoLength', 'Video length must be a positive number').optional().isFloat({ min: 0 }),
    // check('prerequisites', 'Prerequisites must be an array').optional().isArray(),
    // check('learningObjectives', 'Learning objectives must be an array').optional().isArray(),
    // check('equipment', 'Equipment must be an array').optional().isArray()
  ],
  createContent
);

router
  .route("/:id")
  .put(authorize("seller", "admin"), updateContent)
  .delete(authorize("seller", "admin"), deleteContent);

// Toggle content status route
router.put(
  "/:id/toggle-status",
  authorize("seller", "admin"),
  toggleContentStatus
);

// File upload route with extended timeout for large files
router.post(
  "/upload",
  protect,
  authorize("seller"),
  // Add timeout middleware for large uploads with enhanced logging
  (req, res, next) => {
    const requestId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    req.uploadRequestId = requestId;

    console.log(`[Upload-${requestId}] New upload request started`);
    console.log(`[Upload-${requestId}] User: ${req.user.id}, IP: ${req.ip}`);
    console.log(`[Upload-${requestId}] Request headers:`, {
      'content-length': req.headers['content-length'],
      'content-type': req.headers['content-type'],
      'user-agent': req.headers['user-agent']?.substring(0, 100),
      'connection': req.headers['connection']
    });

    // Set longer timeout for this route (1 hour for large video uploads)
    req.setTimeout(3600000); // 1 hour (3600 seconds)
    res.setTimeout(3600000); // 1 hour (3600 seconds)

    // Check content length header
    const contentLength = parseInt(req.headers['content-length']);
    if (contentLength) {
      const contentLengthMB = Math.round(contentLength / (1024 * 1024));
      console.log(`[Upload-${requestId}] Content length: ${contentLengthMB}MB (${contentLength} bytes)`);

      // For multipart uploads, we can't determine the file type from content-type header
      // (it will be multipart/form-data), so we'll use the maximum allowed size (1GB)
      // The actual file type validation will be done by multer's fileFilter
      const maxSize = 1024 * 1024 * 1024; // 1GB - maximum allowed for any file type

      if (contentLength > maxSize) {
        const maxSizeMB = maxSize / (1024 * 1024);
        console.log(`[Upload-${requestId}] File too large: ${contentLengthMB}MB > ${maxSizeMB}MB`);
        return res.status(413).json({
          success: false,
          message: `File size exceeds the maximum limit of ${maxSizeMB}MB`
        });
      }
    } else {
      console.log(`[Upload-${requestId}] No content-length header found`);
    }

    // Add request timeout handlers
    req.on('timeout', () => {
      console.error(`[Upload-${requestId}] Request timeout after 1 hour`);
    });

    res.on('timeout', () => {
      console.error(`[Upload-${requestId}] Response timeout after 1 hour`);
    });

    next();
  },
  upload.single("file"),
  async (req, res, next) => {
    const requestId = req.uploadRequestId || 'unknown';
    const startTime = Date.now();

    try {
      console.log(`[Upload-${requestId}] Multer processing started`);

      if (!req.file) {
        console.log(`[Upload-${requestId}] No file received by multer`);
        return res.status(400).json({
          success: false,
          message: "No file uploaded"
        });
      }

      const fileUrl = getFileUrl(req.file);
      const fileSizeMB = Math.round(req.file.size / (1024 * 1024));
      const processingTime = Date.now() - startTime;

      console.log(`[Upload-${requestId}] Upload completed successfully:`);
      console.log(`[Upload-${requestId}] File: ${req.file.originalname} (${fileSizeMB}MB)`);
      console.log(`[Upload-${requestId}] Processing time: ${processingTime}ms`);
      console.log(`[Upload-${requestId}] File details:`, {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        filename: req.file.filename,
        path: req.file.path,
        location: req.file.location,
        bucket: req.file.bucket,
        key: req.file.key,
        etag: req.file.etag
      });

      res.status(200).json({
        success: true,
        data: {
          fileUrl: fileUrl,
          fileName: req.file.originalname,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
          fileSizeMB: fileSizeMB
        },
        message: `File uploaded successfully (${fileSizeMB}MB). Preview will be generated when content is created.`
      });
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`[Upload-${requestId}] Upload error after ${processingTime}ms:`, {
        message: error.message,
        code: error.code,
        statusCode: error.statusCode,
        originalError: error.originalError?.message,
        uploadDuration: error.uploadDuration,
        stack: error.stack?.substring(0, 500)
      });

      // Send user-friendly error message
      const statusCode = error.statusCode || 500;
      const message = error.message || 'File upload failed. Please try again.';

      res.status(statusCode).json({
        success: false,
        message: message,
        status: statusCode,
        errors: null
      });
    }
  }
);

// Get signed URL for S3 files
router.get(
  "/file/:fileKey(*)",
  protect,
  async (req, res, next) => {
    try {
      const fileKey = req.params.fileKey;

      if (!fileKey) {
        return res.status(400).json({
          success: false,
          message: "File key is required"
        });
      }

      console.log(`[File Access] Generating signed URL for: ${fileKey}`);

      // Generate signed URL (valid for 24 hours)
      const signedUrl = getSignedUrl(fileKey, 86400);

      res.status(200).json({
        success: true,
        data: {
          signedUrl: signedUrl,
          fileKey: fileKey,
          expiresIn: 86400 // 24 hours
        }
      });
    } catch (error) {
      console.error('[File Access] Error generating signed URL:', error);
      next(error);
    }
  }
);



// Test preview generation route (development only)
if (process.env.NODE_ENV !== 'production') {
  router.post(
    "/test-preview",
    protect,
    authorize("seller", "admin"),
    testPreviewGeneration
  );
}

module.exports = router;
