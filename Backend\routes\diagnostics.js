const express = require('express');
const multer = require('multer');
const multerS3 = require('multer-s3');
const { protect, authorize } = require('../middleware/auth');
const { getS3Instance, hasAWSCredentials, isUsingS3Storage } = require('../utils/storageHelper');
const { upload } = require('../utils/fileUpload');
const networkMonitor = require('../utils/networkMonitor');

const router = express.Router();

// Get S3 instance for testing (may be null if credentials missing)
const s3 = getS3Instance();

// Test S3 connectivity
router.get('/s3-test', protect, authorize('admin', 'seller'), async (req, res) => {
  try {
    console.log('🔍 Testing S3 connectivity...');

    // Test 1: Check AWS credentials
    const credentialsTest = {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID ? '✅ Set' : '❌ Missing',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ? '✅ Set' : '❌ Missing',
      region: process.env.AWS_REGION || '❌ Missing',
      bucketName: process.env.AWS_BUCKET_NAME || '❌ Missing',
      allCredentialsPresent: hasAWSCredentials() ? '✅ All present' : '❌ Some missing',
      storageMode: isUsingS3Storage() ? '✅ S3 Storage' : '❌ Local Storage (fallback)'
    };

    // Test 2: List buckets (basic connectivity test)
    let bucketsTest = '❌ Failed';
    let bucketExists = '❌ Not found';
    let bucketDetails = null;

    if (!s3) {
      bucketsTest = '❌ S3 not configured (missing credentials)';
      bucketExists = '❌ S3 not available';
    } else {
      try {
        const buckets = await s3.listBuckets().promise();
        bucketsTest = '✅ Connected';

        // Check if our bucket exists
        const targetBucket = buckets.Buckets.find(bucket => bucket.Name === process.env.AWS_BUCKET_NAME);
        if (targetBucket) {
          bucketExists = '✅ Found';
          bucketDetails = targetBucket;

          // Test bucket permissions
          try {
            await s3.headBucket({ Bucket: process.env.AWS_BUCKET_NAME }).promise();
            bucketExists += ' (Accessible)';
          } catch (permError) {
            bucketExists += ' (Permission denied)';
          }
        }
      } catch (error) {
        bucketsTest = `❌ Error: ${error.message}`;
      }
    }

    // Test 3: Try to upload a small test file
    let uploadTest = '❌ Failed';
    let testFileUrl = null;

    if (!s3) {
      uploadTest = '❌ S3 not configured (missing credentials)';
    } else {
      try {
        const testContent = 'S3 connectivity test file';
        const testKey = `test/connectivity-test-${Date.now()}.txt`;

        const uploadParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: testKey,
          Body: testContent,
          ContentType: 'text/plain'
          // Removed ACL setting - bucket policy handles public access
        };

        const uploadResult = await s3.upload(uploadParams).promise();
        uploadTest = '✅ Success';
        testFileUrl = uploadResult.Location;

        // Clean up test file
        setTimeout(async () => {
          try {
            await s3.deleteObject({ Bucket: process.env.AWS_BUCKET_NAME, Key: testKey }).promise();
            console.log('🧹 Test file cleaned up');
          } catch (cleanupError) {
            console.log('⚠️ Failed to clean up test file:', cleanupError.message);
          }
        }, 5000);

      } catch (uploadError) {
        uploadTest = `❌ Error: ${uploadError.message}`;
      }
    }

    // Test 4: Check multer-s3 configuration
    const multerS3Config = {
      configured: '✅ Configured',
      bucket: process.env.AWS_BUCKET_NAME,
      acl: 'Disabled (using bucket policy)',
      region: process.env.AWS_REGION
    };

    res.status(200).json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        tests: {
          credentials: credentialsTest,
          connectivity: bucketsTest,
          bucketAccess: bucketExists,
          uploadTest: uploadTest,
          multerS3: multerS3Config
        },
        bucketDetails,
        testFileUrl,
        recommendations: [
          bucketsTest.includes('❌') ? 'Check AWS credentials and network connectivity' : null,
          bucketExists.includes('❌') ? 'Create the S3 bucket or check bucket name' : null,
          uploadTest.includes('❌') ? 'Check bucket permissions and IAM policies' : null
        ].filter(Boolean)
      }
    });

  } catch (error) {
    console.error('S3 Test Error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Test file upload with S3
router.post('/test-upload', protect, authorize('admin', 'seller'), (req, res) => {
  // Check if S3 is available
  if (!s3) {
    return res.status(400).json({
      success: false,
      error: 'S3 not configured - missing AWS credentials. Using local storage instead.'
    });
  }

  // Configure test upload to S3
  const testS3Storage = multerS3({
    s3: s3,
    bucket: process.env.AWS_BUCKET_NAME,
    // Removed ACL setting - bucket policy handles public access
    metadata: function (req, file, cb) {
      cb(null, {
        fieldName: file.fieldname,
        testUpload: 'true',
        timestamp: Date.now().toString()
      });
    },
    key: function (req, file, cb) {
      const fileName = `test-uploads/${Date.now()}-${file.originalname.replace(/\s+/g, '-')}`;
      cb(null, fileName);
    }
  });

  const testUpload = multer({
    storage: testS3Storage,
    limits: { fileSize: 10000000 }, // 10MB for testing
    fileFilter: function (req, file, cb) {
      // Allow common file types for testing
      const allowedTypes = /jpeg|jpg|png|gif|mp4|pdf|txt/;
      const extname = allowedTypes.test(file.originalname.toLowerCase());
      const mimetype = allowedTypes.test(file.mimetype);
      
      if (mimetype && extname) {
        return cb(null, true);
      } else {
        cb(new Error('File type not allowed for testing'));
      }
    }
  });

  testUpload.single('testFile')(req, res, (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        message: 'Test file uploaded successfully to S3',
        fileUrl: req.file.location,
        fileName: req.file.originalname,
        fileType: req.file.mimetype,
        fileSize: req.file.size,
        s3Key: req.file.key,
        bucket: req.file.bucket,
        etag: req.file.etag
      }
    });
  });
});

// Get S3 bucket contents
router.get('/s3-contents', protect, authorize('admin'), async (req, res) => {
  try {
    if (!s3) {
      return res.status(400).json({
        success: false,
        error: 'S3 not configured - missing AWS credentials'
      });
    }

    const params = {
      Bucket: process.env.AWS_BUCKET_NAME,
      MaxKeys: 100
    };

    const data = await s3.listObjectsV2(params).promise();
    
    const contents = data.Contents.map(obj => ({
      key: obj.Key,
      size: obj.Size,
      lastModified: obj.LastModified,
      storageClass: obj.StorageClass,
      url: `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${obj.Key}`
    }));

    res.status(200).json({
      success: true,
      data: {
        bucket: process.env.AWS_BUCKET_NAME,
        totalObjects: data.KeyCount,
        isTruncated: data.IsTruncated,
        contents: contents
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test main upload configuration
router.post('/test-main-upload', protect, authorize('admin', 'seller'), (req, res) => {
  console.log('[Diagnostics] Testing main upload configuration...');

  upload.single('testFile')(req, res, (err) => {
    if (err) {
      console.error('[Diagnostics] Upload error:', err);
      return res.status(400).json({
        success: false,
        error: err.message,
        details: {
          storageType: isUsingS3Storage() ? 'S3' : 'Local',
          hasCredentials: hasAWSCredentials(),
          s3Available: !!s3
        }
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    console.log('[Diagnostics] Upload successful:', req.file);

    res.status(200).json({
      success: true,
      data: {
        message: 'File uploaded successfully using main configuration',
        storageType: isUsingS3Storage() ? 'S3' : 'Local',
        fileUrl: req.file.location || `/uploads/${req.file.filename}`,
        fileName: req.file.originalname,
        fileType: req.file.mimetype,
        fileSize: req.file.size,
        s3Details: req.file.location ? {
          s3Key: req.file.key,
          bucket: req.file.bucket,
          etag: req.file.etag
        } : null
      }
    });
  });
});

// Network monitoring diagnostics
router.get('/network-status', protect, authorize('admin', 'seller'), (req, res) => {
  try {
    const stuckUploads = networkMonitor.getStuckUploads();

    res.json({
      success: true,
      data: {
        metrics: networkMonitor.metrics,
        stuckUploads: stuckUploads,
        activeConnections: Array.from(networkMonitor.connections.entries()).map(([id, conn]) => ({
          uploadId: id,
          status: conn.status,
          progress: conn.progress,
          fileName: conn.metadata?.fileName,
          fileSize: conn.metadata?.fileSize,
          elapsedTime: conn.startTime ? Math.round((Date.now() - conn.startTime) / 1000) + 's' : 'unknown',
          lastActivity: new Date(conn.lastActivity).toISOString()
        }))
      }
    });
  } catch (error) {
    console.error('Network status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get network status',
      error: error.message
    });
  }
});

module.exports = router;
