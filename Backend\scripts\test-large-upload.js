/**
 * Test Script for Large File Upload Debugging
 * This script helps test and debug large file uploads
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

// Configuration
const SERVER_URL = process.env.SERVER_URL || 'http://localhost:5000';
const TEST_FILE_SIZE = 100 * 1024 * 1024; // 100MB test file
const AUTH_TOKEN = process.env.TEST_AUTH_TOKEN; // Set this to a valid seller token

/**
 * Create a test file of specified size
 * @param {string} filePath - Path where to create the file
 * @param {number} sizeInBytes - Size of file in bytes
 */
function createTestFile(filePath, sizeInBytes) {
  console.log(`Creating test file: ${filePath} (${Math.round(sizeInBytes / (1024 * 1024))}MB)`);
  
  const writeStream = fs.createWriteStream(filePath);
  const chunkSize = 1024 * 1024; // 1MB chunks
  const totalChunks = Math.ceil(sizeInBytes / chunkSize);
  
  for (let i = 0; i < totalChunks; i++) {
    const isLastChunk = i === totalChunks - 1;
    const currentChunkSize = isLastChunk ? sizeInBytes % chunkSize : chunkSize;
    const chunk = Buffer.alloc(currentChunkSize, `chunk-${i}-`.repeat(Math.floor(currentChunkSize / 10)));
    writeStream.write(chunk);
  }
  
  writeStream.end();
  console.log(`Test file created: ${filePath}`);
}

/**
 * Test file upload with detailed logging
 * @param {string} filePath - Path to file to upload
 */
async function testUpload(filePath) {
  if (!AUTH_TOKEN) {
    console.error('❌ AUTH_TOKEN environment variable is required');
    console.log('Set it to a valid seller JWT token');
    return;
  }

  console.log(`\n🚀 Starting upload test for: ${filePath}`);
  
  const stats = fs.statSync(filePath);
  const fileSizeMB = Math.round(stats.size / (1024 * 1024));
  console.log(`File size: ${fileSizeMB}MB`);

  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));
  form.append('type', 'content');

  const startTime = Date.now();
  let lastProgressTime = startTime;
  let lastProgressPercent = 0;

  try {
    const response = await axios.post(`${SERVER_URL}/api/content/upload`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      timeout: 3600000, // 1 hour
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        const currentTime = Date.now();
        const timeSinceLastProgress = currentTime - lastProgressTime;
        
        // Log every 5% or every 30 seconds
        if (percent >= lastProgressPercent + 5 || timeSinceLastProgress > 30000) {
          const elapsedSeconds = Math.round((currentTime - startTime) / 1000);
          const loadedMB = Math.round(progressEvent.loaded / (1024 * 1024));
          const totalMB = Math.round(progressEvent.total / (1024 * 1024));
          const speed = loadedMB / (elapsedSeconds || 1);
          
          console.log(`📊 Progress: ${percent}% (${loadedMB}MB/${totalMB}MB) - ${speed.toFixed(2)} MB/s - ${elapsedSeconds}s elapsed`);
          
          lastProgressPercent = percent;
          lastProgressTime = currentTime;
        }
      }
    });

    const totalTime = Math.round((Date.now() - startTime) / 1000);
    const avgSpeed = fileSizeMB / (totalTime || 1);
    
    console.log(`\n✅ Upload successful!`);
    console.log(`Total time: ${totalTime}s`);
    console.log(`Average speed: ${avgSpeed.toFixed(2)} MB/s`);
    console.log(`Response:`, response.data);
    
  } catch (error) {
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    
    console.error(`\n❌ Upload failed after ${totalTime}s`);
    console.error(`Error details:`, {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    
    if (error.code === 'ECONNRESET') {
      console.error('🔍 Connection was reset - this indicates a network issue');
    } else if (error.code === 'ECONNABORTED') {
      console.error('🔍 Connection was aborted - likely a timeout');
    } else if (error.response?.status === 413) {
      console.error('🔍 File too large error');
    } else if (error.response?.status >= 500) {
      console.error('🔍 Server error - check server logs');
    }
  }
}

/**
 * Test network monitoring endpoint
 */
async function testNetworkStatus() {
  if (!AUTH_TOKEN) {
    console.error('❌ AUTH_TOKEN required for network status test');
    return;
  }

  try {
    console.log('\n🔍 Checking network status...');
    
    const response = await axios.get(`${SERVER_URL}/api/diagnostics/network-status`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    console.log('📊 Network Status:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Network status check failed:', error.message);
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Large File Upload Test Script');
  console.log('================================');
  
  const testFilePath = path.join(__dirname, 'test-upload.bin');
  
  // Clean up any existing test file
  if (fs.existsSync(testFilePath)) {
    fs.unlinkSync(testFilePath);
  }
  
  try {
    // Create test file
    createTestFile(testFilePath, TEST_FILE_SIZE);
    
    // Test network status before upload
    await testNetworkStatus();
    
    // Test upload
    await testUpload(testFilePath);
    
    // Test network status after upload
    await testNetworkStatus();
    
  } finally {
    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log('\n🧹 Test file cleaned up');
    }
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createTestFile, testUpload, testNetworkStatus };
