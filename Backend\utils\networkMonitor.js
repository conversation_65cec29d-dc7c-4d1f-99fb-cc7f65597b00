/**
 * Network Monitoring Utility
 * Helps track network issues during large file uploads
 */

const os = require('os');
const { performance } = require('perf_hooks');

class NetworkMonitor {
  constructor() {
    this.connections = new Map();
    this.metrics = {
      activeUploads: 0,
      totalUploads: 0,
      failedUploads: 0,
      avgUploadTime: 0,
      networkErrors: 0
    };
  }

  /**
   * Start monitoring an upload
   * @param {string} uploadId - Unique upload identifier
   * @param {Object} metadata - Upload metadata
   */
  startUpload(uploadId, metadata = {}) {
    const startTime = performance.now();
    
    this.connections.set(uploadId, {
      startTime,
      metadata,
      status: 'active',
      progress: 0,
      lastActivity: Date.now()
    });
    
    this.metrics.activeUploads++;
    this.metrics.totalUploads++;
    
    console.log(`[NetworkMonitor] Started monitoring upload: ${uploadId}`);
    console.log(`[NetworkMonitor] Active uploads: ${this.metrics.activeUploads}`);
    
    // Log system resources
    this.logSystemResources(uploadId);
  }

  /**
   * Update upload progress
   * @param {string} uploadId - Upload identifier
   * @param {number} progress - Progress percentage (0-100)
   * @param {number} bytesUploaded - Bytes uploaded so far
   */
  updateProgress(uploadId, progress, bytesUploaded = 0) {
    const connection = this.connections.get(uploadId);
    if (!connection) return;

    const now = Date.now();
    const timeSinceLastActivity = now - connection.lastActivity;
    
    connection.progress = progress;
    connection.lastActivity = now;
    connection.bytesUploaded = bytesUploaded;
    
    // Log if progress is stuck (no activity for more than 30 seconds)
    if (timeSinceLastActivity > 30000 && progress < 100) {
      console.warn(`[NetworkMonitor] Upload ${uploadId} may be stuck - no progress for ${Math.round(timeSinceLastActivity/1000)}s`);
      this.logSystemResources(uploadId);
    }
    
    // Log every 10% progress
    if (progress % 10 === 0 && progress > connection.lastLoggedProgress) {
      const elapsedTime = performance.now() - connection.startTime;
      const speed = bytesUploaded / (elapsedTime / 1000); // bytes per second
      const speedMBps = (speed / (1024 * 1024)).toFixed(2);
      
      console.log(`[NetworkMonitor] Upload ${uploadId}: ${progress}% (${speedMBps} MB/s)`);
      connection.lastLoggedProgress = progress;
    }
  }

  /**
   * Mark upload as completed
   * @param {string} uploadId - Upload identifier
   * @param {boolean} success - Whether upload was successful
   * @param {Object} error - Error object if failed
   */
  completeUpload(uploadId, success = true, error = null) {
    const connection = this.connections.get(uploadId);
    if (!connection) return;

    const endTime = performance.now();
    const duration = endTime - connection.startTime;
    
    connection.status = success ? 'completed' : 'failed';
    connection.endTime = endTime;
    connection.duration = duration;
    connection.error = error;
    
    this.metrics.activeUploads--;
    
    if (success) {
      // Update average upload time
      this.metrics.avgUploadTime = (this.metrics.avgUploadTime + duration) / 2;
      console.log(`[NetworkMonitor] Upload ${uploadId} completed successfully in ${Math.round(duration)}ms`);
    } else {
      this.metrics.failedUploads++;
      if (error && this.isNetworkError(error)) {
        this.metrics.networkErrors++;
      }
      console.error(`[NetworkMonitor] Upload ${uploadId} failed after ${Math.round(duration)}ms:`, error?.message);
    }
    
    // Keep connection data for 5 minutes for debugging
    setTimeout(() => {
      this.connections.delete(uploadId);
    }, 5 * 60 * 1000);
    
    this.logMetrics();
  }

  /**
   * Check if error is network-related
   * @param {Error} error - Error object
   * @returns {boolean} True if network error
   */
  isNetworkError(error) {
    const networkErrorCodes = [
      'ECONNRESET', 'ECONNABORTED', 'ETIMEDOUT', 'ENOTFOUND',
      'ENETUNREACH', 'EHOSTUNREACH', 'EPIPE', 'ECONNREFUSED'
    ];
    
    const networkErrorMessages = [
      'network error', 'connection reset', 'timeout', 'connection lost',
      'socket hang up', 'request aborted'
    ];
    
    return networkErrorCodes.includes(error.code) ||
           networkErrorMessages.some(msg => error.message?.toLowerCase().includes(msg));
  }

  /**
   * Log current system resources
   * @param {string} uploadId - Upload identifier for context
   */
  logSystemResources(uploadId) {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const loadAvg = os.loadavg();
    
    console.log(`[NetworkMonitor-${uploadId}] System resources:`, {
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB'
      },
      cpu: {
        user: Math.round(cpuUsage.user / 1000) + 'ms',
        system: Math.round(cpuUsage.system / 1000) + 'ms'
      },
      loadAverage: loadAvg.map(load => load.toFixed(2)),
      uptime: Math.round(process.uptime()) + 's'
    });
  }

  /**
   * Log current metrics
   */
  logMetrics() {
    console.log(`[NetworkMonitor] Current metrics:`, {
      activeUploads: this.metrics.activeUploads,
      totalUploads: this.metrics.totalUploads,
      failedUploads: this.metrics.failedUploads,
      successRate: ((this.metrics.totalUploads - this.metrics.failedUploads) / this.metrics.totalUploads * 100).toFixed(1) + '%',
      avgUploadTime: Math.round(this.metrics.avgUploadTime) + 'ms',
      networkErrors: this.metrics.networkErrors
    });
  }

  /**
   * Get stuck uploads (no activity for more than 60 seconds)
   * @returns {Array} Array of stuck upload IDs
   */
  getStuckUploads() {
    const now = Date.now();
    const stuckUploads = [];
    
    for (const [uploadId, connection] of this.connections) {
      if (connection.status === 'active' && (now - connection.lastActivity) > 60000) {
        stuckUploads.push({
          uploadId,
          progress: connection.progress,
          stuckFor: Math.round((now - connection.lastActivity) / 1000) + 's',
          metadata: connection.metadata
        });
      }
    }
    
    return stuckUploads;
  }

  /**
   * Start periodic monitoring
   */
  startPeriodicMonitoring() {
    setInterval(() => {
      const stuckUploads = this.getStuckUploads();
      if (stuckUploads.length > 0) {
        console.warn(`[NetworkMonitor] Found ${stuckUploads.length} stuck uploads:`, stuckUploads);
      }
      
      // Log metrics every 5 minutes if there are active uploads
      if (this.metrics.activeUploads > 0) {
        this.logMetrics();
      }
    }, 60000); // Check every minute
  }
}

// Create singleton instance
const networkMonitor = new NetworkMonitor();

// Start periodic monitoring
networkMonitor.startPeriodicMonitoring();

module.exports = networkMonitor;
